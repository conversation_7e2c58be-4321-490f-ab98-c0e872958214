package com.meow.backend.strategy.impl;

import com.meow.backend.model.dto.UnifiedGenerateRequestDTO;
import com.meow.backend.model.dto.V2GeneratorDTO;
import com.meow.backend.model.enums.TaskType;
import org.springframework.stereotype.Component;

/**
 * 写真包策略
 */
@Component
public class StylePackageStrategy extends AbstractGenerateStrategy {

    @Override
    public TaskType getSupportedTaskType() {
        return TaskType.STYLE_PACKAGE;
    }

    @Override
    protected Object doGenerate(UnifiedGenerateRequestDTO request) {
        // 将统一请求转换为V2GeneratorDTO
        V2GeneratorDTO v2GeneratorDTO = convertToV2GeneratorDTO(request);
        return fileUploadRecordService.stylePackageGenerate(v2GeneratorDTO);
    }

    @Override
    protected Object doRetry(Long styleId, Long fileProcessResultId) {
        return fileUploadRecordService.stylePackageRetryGenerate(styleId, fileProcessResultId);
    }

    /**
     * 将统一请求转换为V2GeneratorDTO
     */
    private V2GeneratorDTO convertToV2GeneratorDTO(UnifiedGenerateRequestDTO request) {
        V2GeneratorDTO v2GeneratorDTO = new V2GeneratorDTO();
        v2GeneratorDTO.setStyleId(request.getStyleId());
        v2GeneratorDTO.setCategoryId(request.getCategoryId());
        v2GeneratorDTO.setFileUploadRecordImageDTOList(request.getFileUploadRecordImageDTOList());
        v2GeneratorDTO.setSegmentResult(request.getSegmentResult());
        return v2GeneratorDTO;
    }
}
