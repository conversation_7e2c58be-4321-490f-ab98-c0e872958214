package com.meow.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.dto.DisplayItemDTO;
import com.meow.admin.model.dto.DisplayItemSortDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.vo.DisplayItemVO;

import java.util.List;
import com.meow.admin.service.DisplayItemService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 展示项管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/display-item")
public class DisplayItemController {
    
    @Autowired
    private DisplayItemService displayItemService;
    
    /**
     * 分页查询展示项
     */
    @GetMapping("/page")
    public Page<DisplayItemVO> getDisplayItemPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) Long displayGroupId,
            @RequestParam(required = false) String itemType) {
        
        return displayItemService.getDisplayItemPage(current, size, displayGroupId, itemType);
    }
    
    /**
     * 根据ID获取展示项
     */
    @GetMapping("/{id}")
    public DisplayItem getDisplayItemById(@PathVariable Long id) {
        return displayItemService.getById(id);
    }
    
    /**
     * 创建展示项
     */
    @PostMapping
    public DisplayItem createDisplayItem(@Valid @RequestBody DisplayItemDTO displayItemDTO) {
        return displayItemService.createDisplayItem(displayItemDTO);
    }
    
    /**
     * 更新展示项
     */
    @PutMapping("/{id}")
    public DisplayItem updateDisplayItem(@PathVariable Long id, @Valid @RequestBody DisplayItemDTO displayItemDTO) {
        return displayItemService.updateDisplayItem(id, displayItemDTO);
    }
    
    /**
     * 删除展示项
     */
    @DeleteMapping("/{id}")
    public void deleteDisplayItem(@PathVariable Long id) {
        displayItemService.deleteDisplayItem(id);
    }

    /**
     * 同步展示项数据
     * 从源平台和版本同步数据到目标平台和版本
     */
    @PostMapping("/sync")
    public String syncDisplayItems(@Valid @RequestBody DisplayItemSyncDTO syncDTO) {
        return displayItemService.syncDisplayItems(syncDTO);
    }

    /**
     * 批量删除展示项数据
     */
    @DeleteMapping("/batch")
    public Integer batchDeleteDisplayItems(@RequestParam String platform, @RequestParam String version) {
        return displayItemService.batchDeleteDisplayItems(platform, version);
    }

    /**
     * 批量更新展示项排序
     */
    @PostMapping("/sort")
    public Boolean updateDisplayItemSort(@Valid @RequestBody List<DisplayItemSortDTO> sortList) {
        log.info("更新展示项排序，数据量: {}", sortList.size());
        return displayItemService.updateDisplayItemSort(sortList);
    }
}
