package com.meow.backend.controller;

import com.meow.backend.exception.ServiceException;
import com.meow.backend.model.dto.PaymentVerificationData;
import com.meow.backend.model.dto.RestoreDTO;
import com.meow.backend.model.dto.VerifyReceiptDTO;
import com.meow.backend.model.entity.Order;
import com.meow.backend.service.ApplePayService;
import com.meow.backend.service.PaymentVerificationService;
import com.meow.result.Result;
import com.meow.result.ResultCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 苹果支付控制器
 */
@Slf4j
@Tag(name = "苹果支付")
@RestController
@RequestMapping("/api/apple-pay")
public class ApplePayController {

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private PaymentVerificationService paymentVerificationService;


    /**
     * 验证收据
     *
     * @param verifyReceiptDTO 验证收据DTO
     * @return 订单信息
     */
    @Operation(summary = "验证收据")
    @PostMapping("/verify")
    public Result<Order> verifyReceipt(@Valid @RequestBody VerifyReceiptDTO verifyReceiptDTO) {
        log.info("验证收据请求 | orderId={}", verifyReceiptDTO.getOrderId());

        try {
            // 1. 验证订单和收据
            Order order = applePayService.validateOrderAndReceipt(verifyReceiptDTO);

            // 2. 使用策略模式处理不同商品类型
            PaymentVerificationData verificationData = PaymentVerificationData.forApple(verifyReceiptDTO);
            Order processedOrder = paymentVerificationService.verifyAndProcessOrder(order, verificationData);

            return Result.success(processedOrder);

        } catch (ServiceException e) {
            log.error("验证收据失败 | orderId={}, code={}, message={}",
                    verifyReceiptDTO.getOrderId(), e.getCode(), e.getMessage());
            return Result.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("验证收据失败 | orderId={}", verifyReceiptDTO.getOrderId(), e);
            return Result.failed(ResultCode.PAYMENT_VERIFY_FAILED);
        }
    }

    /**
     * 处理苹果服务器通知 V2 版本
     * 参考文档：https://developer.apple.com/documentation/appstoreservernotifications/responding_to_app_store_server_notifications
     *
     * @param signedPayload JWT格式的签名负载
     * @return 处理结果
     */
    @Operation(summary = "处理苹果服务器通知V2")
    @PostMapping("/notification/v2")
    public ResponseEntity<Void> handleNotificationV2(@RequestBody String signedPayload) {
        log.info("处理苹果服务器通知V2请求 | signedPayload={}", signedPayload);

        try {
            boolean result = applePayService.handleServerNotificationV2(signedPayload);

            // 根据苹果规范，返回200状态码表示成功接收
            if (result) {
                log.info("服务器通知V2处理完成");
                return ResponseEntity.ok().build();
            } else {
                // 如果处理失败，返回500状态码，苹果服务器会重试
                log.error("服务器通知V2处理失败");
                return ResponseEntity.status(500).build();
            }
        } catch (Exception e) {
            log.error("处理服务器通知V2失败", e);
            // 返回500状态码，苹果服务器会在稍后重试
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取苹果通知历史记录
     *
     * @param startDate        开始日期（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDate          结束日期（格式：yyyy-MM-dd HH:mm:ss）
     * @param paginationToken  分页令牌
     * @param notificationType 通知类型
     * @return 是否成功处理
     */
    @Operation(summary = "获取苹果通知历史记录")
    @PostMapping("/notification-history")
    public Result<Boolean> fetchNotificationHistory(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String paginationToken,
            @RequestParam(required = false) String notificationType) {
        log.info("获取苹果通知历史记录请求 | startDate={}, endDate={}, type={}", startDate, endDate, notificationType);
        try {
            boolean result = applePayService.fetchNotificationHistory(startDate, endDate, paginationToken, notificationType);
            return Result.success(result);
        } catch (ServiceException e) {
            log.error("获取通知历史记录失败 | code={}, message={}", e.getCode(), e.getMessage());
            return Result.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取通知历史记录失败", e);
            return Result.failed(ResultCode.APPLE_NOTIFICATION_API_REQUEST_FAILED);
        }
    }

    /**
     * 手动触发获取通知历史记录任务
     *
     * @return 是否成功触发
     */
    @Operation(summary = "手动触发获取通知历史记录任务")
    @PostMapping("/trigger-notification-history-task")
    public Result<Boolean> triggerNotificationHistoryTask() {
        log.info("手动触发获取通知历史记录任务");
        try {
            boolean result = applePayService.scheduledFetchNotificationHistory();
            return Result.success(result);
        } catch (Exception e) {
            log.error("触发通知历史记录任务失败", e);
            return Result.failed(ResultCode.APPLE_NOTIFICATION_API_REQUEST_FAILED);
        }
    }


    /**
     * 客户端restore恢复权益
     *
     * @param restoreDTO 恢复权益
     * @return 续订结果
     */
    @Operation(summary = "客户端主动restore恢复权益")
    @PostMapping("/restore")
    public Result<Boolean> restoreSubscription(@Valid @RequestBody RestoreDTO restoreDTO) {
        log.info("客户端restore恢复权益 | restoreDTO={}", restoreDTO);
        try {
            boolean result = applePayService.restoreSubscription(restoreDTO);
            return Result.success(result);
        } catch (ServiceException e) {
            log.error("客户端restore恢复权益 | code={}, message={}", e.getCode(), e.getMessage());
            return Result.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("客户端restore恢复权益", e);
            return Result.failed(ResultCode.RENEWAL_PROCESS_FAILED);
        }
    }

}