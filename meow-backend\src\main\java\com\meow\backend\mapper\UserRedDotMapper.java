package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.UserRedDot;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户红点Mapper
 */
public interface UserRedDotMapper extends BaseMapper<UserRedDot> {
    
    /**
     * 获取用户各模块红点统计
     */
    List<Map<String, Object>> getUserModuleRedDotStats(@Param("userId") Long userId);
    
    /**
     * 获取用户指定模块的红点详情
     */
    List<UserRedDot> getUserModuleRedDotDetails(@Param("userId") Long userId,
                                               @Param("moduleCode") String moduleCode);
    
    /**
     * 批量更新红点状态为已读
     */
    int markModuleAsRead(@Param("userId") Long userId, @Param("moduleCode") String moduleCode);
    
    /**
     * 更新指定业务的红点状态
     */
    int markBusinessAsRead(@Param("userId") Long userId,
                          @Param("moduleCode") String moduleCode,
                          @Param("businessId") Long businessId);

    /**
     * 批量查询指定模块的指定业务红点状态
     */
    List<Map<String, Object>> findRedDotStatusBatch(@Param("userId")Long userId,
                                                   @Param("moduleCode") String taskResult,
                                                   @Param("businessIds") List<Long> fileProcessResultIds);
}
