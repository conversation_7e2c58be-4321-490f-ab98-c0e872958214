package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.DisplayGroupMapper;
import com.meow.admin.mapper.DisplayItemMapper;
import com.meow.admin.mapper.StyleVariantMapper;
import com.meow.admin.mapper.CategoryMapper;
import com.meow.admin.model.dto.DisplayItemDTO;
import com.meow.admin.model.dto.DisplayItemSortDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.entity.StyleVariant;
import com.meow.admin.model.entity.Category;
import com.meow.admin.model.vo.DisplayItemVO;
import com.meow.admin.service.DisplayItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展示项服务实现类
 */
@Slf4j
@Service
public class DisplayItemServiceImpl extends ServiceImpl<DisplayItemMapper, DisplayItem> implements DisplayItemService {

    @Autowired
    private DisplayGroupMapper displayGroupMapper;

    @Autowired
    private StyleVariantMapper styleVariantMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public Page<DisplayItemVO> getDisplayItemPage(Long current, Long size, Long displayGroupId, String itemType) {
        log.info("分页查询展示项 | current={}, size={}, displayGroupId={}, itemType={}",
                current, size, displayGroupId, itemType);

        Page<DisplayItemVO> page = new Page<>(current, size);
        return baseMapper.selectDisplayItemPage(page, displayGroupId, itemType);
    }

    @Override
    public DisplayItem createDisplayItem(DisplayItemDTO displayItemDTO) {
        log.info("创建展示项 | displayItemDTO={}", displayItemDTO);

        DisplayItem displayItem = new DisplayItem();
        BeanUtils.copyProperties(displayItemDTO, displayItem);
        displayItem.setIsDeleted(false);
        displayItem.setCreatedAt(LocalDateTime.now());
        displayItem.setUpdatedAt(LocalDateTime.now());

        // 设置默认值
        if (displayItem.getClickCount() == null) {
            displayItem.setClickCount(0L);
        }
        if (displayItem.getSortOrder() == null) {
            displayItem.setSortOrder(0);
        }

        baseMapper.insert(displayItem);
        log.info("展示项创建成功 | id={}", displayItem.getId());

        return displayItem;
    }

    @Override
    public DisplayItem updateDisplayItem(Long id, DisplayItemDTO displayItemDTO) {
        log.info("更新展示项 | id={}, displayItemDTO={}", id, displayItemDTO);

        DisplayItem existingItem = baseMapper.selectById(id);
        if (existingItem == null || existingItem.getIsDeleted()) {
            throw new RuntimeException("展示项不存在: " + id);
        }

        BeanUtils.copyProperties(displayItemDTO, existingItem);
        existingItem.setUpdatedAt(LocalDateTime.now());

        baseMapper.updateById(existingItem);
        log.info("展示项更新成功 | id={}", id);

        return existingItem;
    }

    @Override
    public void deleteDisplayItem(Long id) {
        log.info("删除展示项 | id={}", id);

        DisplayItem displayItem = baseMapper.selectById(id);
        if (displayItem == null || displayItem.getIsDeleted()) {
            throw new RuntimeException("展示项不存在: " + id);
        }

        displayItem.setIsDeleted(true);
        displayItem.setUpdatedAt(LocalDateTime.now());

        baseMapper.updateById(displayItem);
        log.info("展示项删除成功 | id={}", id);
    }

    @Override
    public String syncDisplayItems(DisplayItemSyncDTO syncDTO) {
        log.info("开始同步展示项数据 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}",
                syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

        try {
            // 1. 先查询源平台的展示组列表（需要ID和Code字段）
            LambdaQueryWrapper<DisplayGroup> sourceGroupQuery = new LambdaQueryWrapper<>();
            sourceGroupQuery.select(DisplayGroup::getId, DisplayGroup::getCode)
                    .eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, syncDTO.getSourcePlatform());
            if (syncDTO.getSourceVersion() != null && !syncDTO.getSourceVersion().isEmpty()) {
                sourceGroupQuery.eq(DisplayGroup::getVersion, syncDTO.getSourceVersion());
            }

            List<DisplayGroup> sourceGroups = displayGroupMapper.selectList(sourceGroupQuery);
            if (sourceGroups.isEmpty()) {
                return "源平台没有找到展示组数据，无法同步展示项";
            }

            log.info("找到源平台展示组数量: {}", sourceGroups.size());

            List<Long> sourceGroupIds = sourceGroups.stream()
                    .map(DisplayGroup::getId)
                    .collect(Collectors.toList());

            // 2. 查询源平台的展示项数据
            LambdaQueryWrapper<DisplayItem> sourceItemQuery = new LambdaQueryWrapper<>();
            sourceItemQuery.eq(DisplayItem::getIsDeleted, false)
                    .in(DisplayItem::getDisplayGroupId, sourceGroupIds);

            List<DisplayItem> sourceItems = baseMapper.selectList(sourceItemQuery);
            log.info("找到源平台展示项数量: {}", sourceItems.size());

            // 3. 查询目标平台的展示组ID映射
            LambdaQueryWrapper<DisplayGroup> targetGroupQuery = new LambdaQueryWrapper<>();
            targetGroupQuery.eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, syncDTO.getTargetPlatform());
            if (syncDTO.getTargetVersion() != null && !syncDTO.getTargetVersion().isEmpty()) {
                targetGroupQuery.eq(DisplayGroup::getVersion, syncDTO.getTargetVersion());
            }

            List<DisplayGroup> targetGroups = displayGroupMapper.selectList(targetGroupQuery);
            log.info("找到目标平台展示组数量: {}", targetGroups.size());

            Map<String, Long> targetGroupCodeToIdMap = targetGroups.stream()
                    .collect(Collectors.toMap(DisplayGroup::getCode, DisplayGroup::getId));

            // 4. 批量查询StyleVariant和Category映射关系，避免循环查询
            Map<Long, Long> styleVariantIdMap = buildStyleVariantIdMap(sourceItems, syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());
            Map<Long, Long> categoryIdMap = buildCategoryIdMap(sourceItems, syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

            // 5. 构建源展示组ID到Code的映射，提高查询效率
            Map<Long, String> sourceGroupIdToCodeMap = sourceGroups.stream()
                    .collect(Collectors.toMap(DisplayGroup::getId, DisplayGroup::getCode));

            // 6. 同步展示项数据
            int syncCount = 0;
            int skippedCount = 0;
            for (DisplayItem sourceItem : sourceItems) {
                // 通过映射快速找到源展示组的code
                String sourceGroupCode = sourceGroupIdToCodeMap.get(sourceItem.getDisplayGroupId());
                if (sourceGroupCode == null) {
                    log.warn("未找到展示项对应的源展示组: displayGroupId={}", sourceItem.getDisplayGroupId());
                    skippedCount++;
                    continue;
                }

                // 找到目标平台对应的展示组ID
                Long targetGroupId = targetGroupCodeToIdMap.get(sourceGroupCode);
                if (targetGroupId == null) {
                    log.warn("未找到目标平台对应的展示组: sourceGroupCode={}", sourceGroupCode);
                    skippedCount++;
                    continue;
                }

                // 检查目标平台是否已存在相同的展示项
                LambdaQueryWrapper<DisplayItem> existingQuery = new LambdaQueryWrapper<>();
                existingQuery.eq(DisplayItem::getDisplayGroupId, targetGroupId)
                        .eq(DisplayItem::getItemType, sourceItem.getItemType())
                        .eq(DisplayItem::getIsDeleted, false);

                if (sourceItem.getStyleVariantId() != null) {
                    existingQuery.eq(DisplayItem::getStyleVariantId, sourceItem.getStyleVariantId());
                }
                if (sourceItem.getCategoryId() != null) {
                    existingQuery.eq(DisplayItem::getCategoryId, sourceItem.getCategoryId());
                }

                DisplayItem existingItem = baseMapper.selectOne(existingQuery);

                if (existingItem == null) {
                    // 创建新的展示项，需要映射到目标版本对应的数据
                    DisplayItem newItem = new DisplayItem();

                    // 基础字段直接复制
                    newItem.setDisplayGroupId(targetGroupId);
                    newItem.setItemType(sourceItem.getItemType());
                    newItem.setIcon(sourceItem.getIcon());
                    newItem.setClickCount(sourceItem.getClickCount());
                    newItem.setSortOrder(sourceItem.getSortOrder());
                    newItem.setDisplayConfig(sourceItem.getDisplayConfig());
                    newItem.setIsDeleted(false);
                    newItem.setCreatedAt(LocalDateTime.now());
                    newItem.setUpdatedAt(LocalDateTime.now());

                    // 处理style_variant_id：使用预构建的映射关系
                    if (sourceItem.getStyleVariantId() != null) {
                        Long targetStyleVariantId = styleVariantIdMap.get(sourceItem.getStyleVariantId());
                        if (targetStyleVariantId != null) {
                            newItem.setStyleVariantId(targetStyleVariantId);
                        } else {
                            log.warn("未找到目标版本对应的StyleVariant: sourceStyleVariantId={}",
                                    sourceItem.getStyleVariantId());
                            skippedCount++;
                            continue;
                        }
                    }

                    // 处理category_id：使用预构建的映射关系
                    if (sourceItem.getCategoryId() != null) {
                        Long targetCategoryId = categoryIdMap.get(sourceItem.getCategoryId());
                        if (targetCategoryId != null) {
                            newItem.setCategoryId(targetCategoryId);
                        } else {
                            log.warn("未找到目标版本对应的Category: sourceCategoryId={}",
                                    sourceItem.getCategoryId());
                            skippedCount++;
                            continue;
                        }
                    }

                    baseMapper.insert(newItem);
                    syncCount++;
                    log.debug("创建新展示项: sourceId={}, targetGroupId={}, targetStyleVariantId={}, targetCategoryId={}",
                            sourceItem.getId(), targetGroupId, newItem.getStyleVariantId(), newItem.getCategoryId());
                } else {
                    // 更新现有展示项
                    existingItem.setIcon(sourceItem.getIcon());
                    existingItem.setSortOrder(sourceItem.getSortOrder());
                    existingItem.setDisplayConfig(sourceItem.getDisplayConfig());
                    existingItem.setUpdatedAt(LocalDateTime.now());

                    baseMapper.updateById(existingItem);
                    log.debug("更新展示项: id={}", existingItem.getId());
                }
            }

            String result = String.format("展示项同步完成！从 %s(%s) 同步到 %s(%s)，共同步 %d 条数据，跳过 %d 条数据",
                    syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                    syncDTO.getTargetPlatform(), syncDTO.getTargetVersion(),
                    syncCount, skippedCount);

            log.info("展示项数据同步完成 | result={}", result);
            return result;

        } catch (Exception e) {
            log.error("展示项数据同步失败", e);
            throw new RuntimeException("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchDeleteDisplayItems(String platform, String version) {
        log.info("开始批量删除展示项数据 | platform={}, version={}", platform, version);

        try {
            // 通过展示组关联查询需要删除的展示项
            LambdaQueryWrapper<DisplayItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DisplayItem::getIsDeleted, false);

            // 这里需要通过展示组来筛选，因为展示项本身没有平台和版本字段
            // 可以通过子查询或者先查询展示组ID再查询展示项

            List<DisplayItem> itemsToDelete = baseMapper.selectList(queryWrapper);
            int deleteCount = 0;

            for (DisplayItem item : itemsToDelete) {
                item.setIsDeleted(true);
                item.setUpdatedAt(LocalDateTime.now());
                baseMapper.updateById(item);
                deleteCount++;
            }

            log.info("展示项批量删除完成 | platform={}, version={}, deleteCount={}", platform, version, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("展示项批量删除失败", e);
            throw new RuntimeException("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量构建StyleVariant ID映射关系
     * 避免在循环中逐个查询，提高性能
     */
    private Map<Long, Long> buildStyleVariantIdMap(List<DisplayItem> sourceItems, DisplayGroup.Platform targetPlatform, String targetVersion) {
        // 1. 收集所有需要映射的StyleVariant ID
        Set<Long> sourceStyleVariantIds = sourceItems.stream()
                .map(DisplayItem::getStyleVariantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (sourceStyleVariantIds.isEmpty()) {
            return new HashMap<>();
        }

        // 2. 批量查询源StyleVariant，根据style_id分组
        List<StyleVariant> sourceStyleVariants = styleVariantMapper.selectBatchIds(sourceStyleVariantIds);
        Map<Long, Long> sourceIdToStyleIdMap = sourceStyleVariants.stream()
                .collect(Collectors.toMap(StyleVariant::getId, StyleVariant::getStyleId));

        // 3. 批量查询目标版本的StyleVariant
        Set<Long> styleIds = new HashSet<>(sourceIdToStyleIdMap.values());
        LambdaQueryWrapper<StyleVariant> targetQuery = new LambdaQueryWrapper<>();
        targetQuery.eq(StyleVariant::getPlatform, targetPlatform)
                .eq(StyleVariant::getIsDeleted, false)
                .in(StyleVariant::getStyleId, styleIds);

        if (targetVersion != null && !targetVersion.isEmpty()) {
            targetQuery.eq(StyleVariant::getVersion, targetVersion);
        }

        List<StyleVariant> targetStyleVariants = styleVariantMapper.selectList(targetQuery);
        Map<Long, Long> styleIdToTargetIdMap = targetStyleVariants.stream()
                .collect(Collectors.toMap(StyleVariant::getStyleId, StyleVariant::getId));

        // 4. 构建源ID到目标ID的映射
        Map<Long, Long> resultMap = new HashMap<>();
        for (Map.Entry<Long, Long> entry : sourceIdToStyleIdMap.entrySet()) {
            Long sourceId = entry.getKey();
            Long styleId = entry.getValue();
            Long targetId = styleIdToTargetIdMap.get(styleId);
            if (targetId != null) {
                resultMap.put(sourceId, targetId);
            }
        }

        log.info("构建StyleVariant映射关系: 源数量={}, 目标数量={}", sourceStyleVariantIds.size(), resultMap.size());
        return resultMap;
    }

    /**
     * 批量构建Category ID映射关系
     * 通过Category的name和type组合进行映射（目前都是一级分类，parent_id=0）
     */
    private Map<Long, Long> buildCategoryIdMap(List<DisplayItem> sourceItems, DisplayGroup.Platform targetPlatform, String targetVersion) {
        // 1. 收集所有需要映射的Category ID
        Set<Long> sourceCategoryIds = sourceItems.stream()
                .map(DisplayItem::getCategoryId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (sourceCategoryIds.isEmpty()) {
            return new HashMap<>();
        }

        // 2. 批量查询源Category
        List<Category> sourceCategories = categoryMapper.selectBatchIds(sourceCategoryIds);
        Map<Long, String> sourceIdToKeyMap = sourceCategories.stream()
                .collect(Collectors.toMap(Category::getId, this::buildCategoryCompositeKey));

        // 3. 批量查询目标版本的所有Category（只查询一级分类）
        LambdaQueryWrapper<Category> targetQuery = new LambdaQueryWrapper<>();
        targetQuery.eq(Category::getPlatform, targetPlatform)
                .eq(Category::getIsDeleted, false)
                .eq(Category::getParentId, 0); // 只查询一级分类

        if (targetVersion != null && !targetVersion.isEmpty()) {
            targetQuery.eq(Category::getVersion, targetVersion);
        }

        List<Category> targetCategories = categoryMapper.selectList(targetQuery);
        Map<String, Long> keyToTargetIdMap = targetCategories.stream()
                .collect(Collectors.toMap(
                        this::buildCategoryCompositeKey,
                        Category::getId,
                        (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));

        // 4. 构建源ID到目标ID的映射
        Map<Long, Long> resultMap = new HashMap<>();
        for (Map.Entry<Long, String> entry : sourceIdToKeyMap.entrySet()) {
            Long sourceId = entry.getKey();
            String key = entry.getValue();
            Long targetId = keyToTargetIdMap.get(key);
            if (targetId != null) {
                resultMap.put(sourceId, targetId);
            } else {
                log.warn("未找到匹配的目标Category: sourceId={}, key={}", sourceId, key);
            }
        }

        log.info("构建Category映射关系: 源数量={}, 目标数量={}, 映射成功={}",
                sourceCategoryIds.size(), targetCategories.size(), resultMap.size());
        return resultMap;
    }

    /**
     * 构建Category的复合键，用于跨版本匹配
     * 使用 name + type + parent_id 的组合作为唯一标识
     */
    private String buildCategoryCompositeKey(Category category) {
        return category.getName() + "|" + category.getType() + "|" + category.getParentId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDisplayItemSort(List<DisplayItemSortDTO> sortList) {
        log.info("批量更新展示项排序 | sortList.size={}", sortList.size());

        try {
            // 验证输入参数
            if (sortList == null || sortList.isEmpty()) {
                log.warn("排序列表为空");
                return false;
            }

            // 验证所有ID的有效性
            List<Long> ids = sortList.stream().map(DisplayItemSortDTO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<DisplayItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DisplayItem::getId, ids)
                       .eq(DisplayItem::getIsDeleted, false);
            List<DisplayItem> existingItems = baseMapper.selectList(queryWrapper);

            if (existingItems.size() != sortList.size()) {
                log.warn("部分展示项不存在或已删除 | 请求数量={}, 有效数量={}", sortList.size(), existingItems.size());
            }

            // 创建ID到DisplayItem的映射
            Map<Long, DisplayItem> itemMap = existingItems.stream()
                    .collect(Collectors.toMap(DisplayItem::getId, item -> item));

            // 批量更新排序
            int updateCount = 0;
            LocalDateTime now = LocalDateTime.now();

            for (DisplayItemSortDTO sortDTO : sortList) {
                DisplayItem displayItem = itemMap.get(sortDTO.getId());
                if (displayItem != null) {
                    displayItem.setSortOrder(sortDTO.getSortOrder());
                    displayItem.setUpdatedAt(now);
                    baseMapper.updateById(displayItem);
                    updateCount++;
                    log.debug("更新展示项排序 | id={}, sortOrder={}", sortDTO.getId(), sortDTO.getSortOrder());
                } else {
                    log.warn("展示项不存在或已删除 | id={}", sortDTO.getId());
                }
            }

            log.info("展示项排序更新完成 | 请求数量={}, 实际更新数量={}", sortList.size(), updateCount);
            return updateCount > 0;

        } catch (Exception e) {
            log.error("展示项排序更新失败", e);
            throw new RuntimeException("排序更新失败: " + e.getMessage());
        }
    }
}
