<template>
  <div class="style-category-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>样式分类管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增关联</el-button>
            <el-button type="warning" @click="handleCompare">数据对比</el-button>
            <el-button type="danger" @click="handleSync">数据同步</el-button>
            <el-button type="success" @click="handleRefresh">刷新</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="样式ID">
          <el-input v-model="queryParams.styleId" placeholder="请输入样式ID" clearable />
        </el-form-item>
        <el-form-item label="分类ID">
          <el-input v-model="queryParams.categoryId" placeholder="请输入分类ID" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 140px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 列表 -->
      <el-table
        v-loading="loading"
        :data="styleCategoryList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="styleId" label="样式ID" width="100" />
        <el-table-column prop="styleTitle" label="样式标题" width="180" show-overflow-tooltip />
        <el-table-column prop="categoryId" label="分类ID" width="100" />
        <el-table-column prop="categoryName" label="分类名称" width="180" show-overflow-tooltip />
        <el-table-column prop="platformText" label="平台" width="100" />
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column prop="sortOrder" label="排序值" width="80" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="样式ID" prop="styleId">
          <el-input-number v-model="form.styleId" :min="1" :controls="false" placeholder="请输入样式ID" style="width: 100%;" />
        </el-form-item>
        
        <el-form-item label="分类ID" prop="categoryId">
          <el-input-number v-model="form.categoryId" :min="1" :controls="false" placeholder="请输入分类ID" style="width: 100%;" />
        </el-form-item>
        
        <el-form-item label="平台" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入版本号，格式如1.0.0" />
        </el-form-item>
        
        <el-form-item label="排序值" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" :controls="false" placeholder="请输入排序值" style="width: 100%;" />
          <div class="tip">值越小，排序越靠前</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 详情对话框 -->
    <el-dialog
      title="样式分类关联详情"
      v-model="detailVisible"
      width="500px"
    >
      <el-descriptions :column="1" border v-if="currentDetail">
        <el-descriptions-item label="ID">{{ currentDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="样式ID">{{ currentDetail.styleId }}</el-descriptions-item>
        <el-descriptions-item label="样式标题">{{ currentDetail.styleTitle }}</el-descriptions-item>
        <el-descriptions-item label="分类ID">{{ currentDetail.categoryId }}</el-descriptions-item>
        <el-descriptions-item label="分类名称">{{ currentDetail.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="平台">{{ currentDetail.platformText }}</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ currentDetail.version }}</el-descriptions-item>
        <el-descriptions-item label="排序值">{{ currentDetail.sortOrder }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentDetail.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentDetail.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    
    <!-- 数据对比对话框 -->
    <el-dialog
      title="版本/平台数据对比"
      v-model="compareDialogVisible"
      width="900px"
      destroy-on-close
      :fullscreen="isFullscreen"
      class="compare-dialog"
    >
      <template #header>
        <div class="dialog-custom-header">
          <span class="dialog-title">版本/平台数据对比</span>
          <el-button
            type="text"
            @click="toggleFullscreen"
            class="fullscreen-btn"
          >

          </el-button>
        </div>
      </template>
      
      <el-form :inline="true" :model="compareForm" class="compare-form">
        <el-form-item label="源平台">
          <el-select v-model="compareForm.sourcePlatform" placeholder="请选择源平台" style="width: 140px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="源版本">
          <el-input v-model="compareForm.sourceVersion" placeholder="请输入源版本" style="width: 120px;" />
        </el-form-item>
        <el-form-item label="目标平台">
          <el-select v-model="compareForm.targetPlatform" placeholder="请选择目标平台" style="width: 140px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标版本">
          <el-input v-model="compareForm.targetVersion" placeholder="请输入目标版本" style="width: 120px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCompareData" :loading="comparing">对比数据</el-button>
        </el-form-item>
      </el-form>
      
      <div v-loading="comparing">
        <div v-if="!compareResult.length && !comparing" class="compare-empty">
          <el-empty description="请选择版本/平台并点击对比数据按钮" />
        </div>
        <div v-if="compareResult.length" class="compare-result">
          <h4>对比结果</h4>
          <div class="summary">
            <p>源数据：{{ compareForm.sourcePlatform }} - {{ compareForm.sourceVersion }} (共 {{ compareSourceCount }} 条记录)</p>
            <p>目标数据：{{ compareForm.targetPlatform }} - {{ compareForm.targetVersion }} (共 {{ compareTargetCount }} 条记录)</p>
            <p>仅在源数据中存在：{{ compareOnlyInSource }} 条记录</p>
            <p>仅在目标数据中存在：{{ compareOnlyInTarget }} 条记录</p>
            <p>存在于两者但有差异：{{ compareDifferent }} 条记录</p>
            <div class="summary-actions">
              <el-button type="primary" @click="handleSyncAll" :disabled="!compareResult.length" :loading="syncing">
                同步全部数据
              </el-button>
            </div>
          </div>
          
          <el-tabs v-model="activeCompareTab" type="card">
            <el-tab-pane label="差异数据" name="different">
              <div v-for="(item, index) in compareResult.filter(item => item.diffType !== 'same')" 
                   :key="index" 
                   class="diff-item">
                <div class="diff-header">
                  <div class="diff-title">
                    <span class="item-title">{{ item.styleTitle }} / {{ item.categoryName }}</span>
                    <el-tag v-if="item.diffType === 'source_only'" type="warning">仅源数据有</el-tag>
                    <el-tag v-else-if="item.diffType === 'target_only'" type="danger">仅目标数据有</el-tag>
                    <el-tag v-else-if="item.diffType === 'different'" type="info">数据不同</el-tag>
                  </div>
                  <div class="diff-actions">
                    <el-button 
                      v-if="item.diffType === 'source_only'" 
                      size="small" 
                      type="success" 
                      @click="handleSyncItem(item.source, 'add')"
                    >同步到目标</el-button>
                    <el-button 
                      v-else-if="item.diffType === 'target_only'" 
                      size="small" 
                      type="warning" 
                      @click="handleSyncItem(item.target, 'delete')"
                    >从目标删除</el-button>
                    <el-button 
                      v-else-if="item.diffType === 'different'" 
                      size="small" 
                      type="primary" 
                      @click="handleSyncItem(item.source, 'update', item.target)"
                    >更新目标</el-button>
                  </div>
                </div>
                
                <div class="diff-content">
                  <div class="diff-source">
                    <div class="diff-panel-header">源数据 ({{ compareForm.sourcePlatform }} - {{ compareForm.sourceVersion }})</div>
                    <div v-if="item.source" class="diff-panel-content">
                      <el-descriptions :column="1" border>
                        <el-descriptions-item label="样式ID" :class="{'highlight-diff': item.diffType === 'different' && item.target && item.source.styleId !== item.target.styleId}">
                          {{ item.source.styleId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类ID" :class="{'highlight-diff': item.diffType === 'different' && item.target && item.source.categoryId !== item.target.categoryId}">
                          {{ item.source.categoryId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="样式标题">
                          {{ item.source.styleTitle }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类名称">
                          {{ item.source.categoryName }}
                        </el-descriptions-item>
                        <el-descriptions-item label="排序值" :class="{'highlight-diff': item.diffType === 'different' && item.target && item.source.sortOrder !== item.target.sortOrder}">
                          {{ item.source.sortOrder }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                    <div v-else class="diff-panel-content diff-empty">
                      <el-empty description="无源数据" />
                    </div>
                  </div>
                  
                  <div class="diff-target">
                    <div class="diff-panel-header">目标数据 ({{ compareForm.targetPlatform }} - {{ compareForm.targetVersion }})</div>
                    <div v-if="item.target" class="diff-panel-content">
                      <el-descriptions :column="1" border>
                        <el-descriptions-item label="样式ID" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.styleId !== item.target.styleId}">
                          {{ item.target.styleId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类ID" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.categoryId !== item.target.categoryId}">
                          {{ item.target.categoryId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="样式标题">
                          {{ item.target.styleTitle }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类名称">
                          {{ item.target.categoryName }}
                        </el-descriptions-item>
                        <el-descriptions-item label="排序值" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.sortOrder !== item.target.sortOrder}">
                          {{ item.target.sortOrder }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                    <div v-else class="diff-panel-content diff-empty">
                      <el-empty description="无目标数据" />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据时展示空状态 -->
              <div v-if="compareResult.filter(item => item.diffType !== 'same').length === 0" class="diff-empty-state">
                <el-empty description="没有差异数据" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="全部数据" name="all">
              <div v-for="(item, index) in compareResult" 
                   :key="index" 
                   class="diff-item">
                <div class="diff-header">
                  <div class="diff-title">
                    <span class="item-title">{{ item.styleTitle }} / {{ item.categoryName }}</span>
                    <el-tag v-if="item.diffType === 'source_only'" type="warning">仅源数据有</el-tag>
                    <el-tag v-else-if="item.diffType === 'target_only'" type="danger">仅目标数据有</el-tag>
                    <el-tag v-else-if="item.diffType === 'different'" type="info">数据不同</el-tag>
                    <el-tag v-else type="success">相同</el-tag>
                  </div>
                  <div class="diff-actions" v-if="item.diffType !== 'same'">
                    <el-button 
                      v-if="item.diffType === 'source_only'" 
                      size="small" 
                      type="success" 
                      @click="handleSyncItem(item.source, 'add')"
                    >同步到目标</el-button>
                    <el-button 
                      v-else-if="item.diffType === 'target_only'" 
                      size="small" 
                      type="warning" 
                      @click="handleSyncItem(item.target, 'delete')"
                    >从目标删除</el-button>
                    <el-button 
                      v-else-if="item.diffType === 'different'" 
                      size="small" 
                      type="primary" 
                      @click="handleSyncItem(item.source, 'update', item.target)"
                    >更新目标</el-button>
                  </div>
                </div>
                
                <div class="diff-content">
                  <div class="diff-source">
                    <div class="diff-panel-header">源数据 ({{ compareForm.sourcePlatform }} - {{ compareForm.sourceVersion }})</div>
                    <div v-if="item.source" class="diff-panel-content">
                                              <el-descriptions :column="1" border>
                        <el-descriptions-item label="样式ID" :class="{'highlight-diff': item.diffType === 'different' && item.target && item.source.styleId !== item.target.styleId}">
                          {{ item.source.styleId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类ID" :class="{'highlight-diff': item.diffType === 'different' && item.target && item.source.categoryId !== item.target.categoryId}">
                          {{ item.source.categoryId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="样式标题">
                          {{ item.source.styleTitle }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类名称">
                          {{ item.source.categoryName }}
                        </el-descriptions-item>
                        <el-descriptions-item label="排序值" :class="{'highlight-diff': item.diffType === 'different' && item.source.sortOrder !== item.target.sortOrder}">
                          {{ item.source.sortOrder }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                    <div v-else class="diff-panel-content diff-empty">
                      <el-empty description="无源数据" />
                    </div>
                  </div>
                  
                  <div class="diff-target">
                    <div class="diff-panel-header">目标数据 ({{ compareForm.targetPlatform }} - {{ compareForm.targetVersion }})</div>
                    <div v-if="item.target" class="diff-panel-content">
                      <el-descriptions :column="1" border>
                        <el-descriptions-item label="样式ID" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.styleId !== item.target.styleId}">
                          {{ item.target.styleId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类ID" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.categoryId !== item.target.categoryId}">
                          {{ item.target.categoryId }}
                        </el-descriptions-item>
                        <el-descriptions-item label="样式标题">
                          {{ item.target.styleTitle }}
                        </el-descriptions-item>
                        <el-descriptions-item label="分类名称">
                          {{ item.target.categoryName }}
                        </el-descriptions-item>
                        <el-descriptions-item label="排序值" :class="{'highlight-diff': item.diffType === 'different' && item.source && item.source.sortOrder !== item.target.sortOrder}">
                          {{ item.target.sortOrder }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                    <div v-else class="diff-panel-content diff-empty">
                      <el-empty description="无目标数据" />
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 无数据时展示空状态 -->
              <div v-if="compareResult.length === 0" class="diff-empty-state">
                <el-empty description="没有数据" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
    
    <!-- 同步对话框 -->
    <el-dialog
      title="批量数据同步"
      v-model="syncDialogVisible"
      width="500px"
      destroy-on-close
    >
      <el-form 
        ref="syncFormRef" 
        :model="syncForm" 
        :rules="syncRules"
        label-width="100px"
      >
        <el-form-item label="源平台" prop="sourcePlatform">
          <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="源版本" prop="sourceVersion">
          <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号，格式如1.0.0" />
        </el-form-item>
        
        <el-form-item label="目标平台" prop="targetPlatform">
          <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标版本" prop="targetVersion">
          <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号，格式如1.0.0" />
        </el-form-item>
        
        <el-form-item label="同步选项">
          <el-checkbox-group v-model="syncForm.options">
            <el-checkbox label="add">添加目标中不存在的数据</el-checkbox>
            <el-checkbox label="update">更新目标中已存在的数据</el-checkbox>
            <el-checkbox label="delete">删除目标中源数据没有的数据</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSyncForm" :loading="syncing">同步数据</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getStyleCategoryList, 
  getStyleCategoryDetail,
  createStyleCategory, 
  updateStyleCategory, 
  deleteStyleCategory,
  syncStyleCategoryData,
  getStyleCategoryByPlatformVersion
} from '@/api/styleCategory'


// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  styleId: '',
  categoryId: '',
  platform: '',
  version: ''
})

// 列表数据
const styleCategoryList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的详情
const currentDetail = ref(null)
const detailVisible = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)
const submitting = ref(false)
const form = reactive({
  id: null,
  styleId: null,
  categoryId: null,
  platform: 'ios',
  version: '1.0.0',
  sortOrder: 0
})

// 表单校验规则
const rules = {
  styleId: [
    { required: true, message: '请输入样式ID', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请输入分类ID', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ]
}

// 数据对比相关
const compareDialogVisible = ref(false)
const compareForm = reactive({
  sourcePlatform: 'ios',
  sourceVersion: '1.0.0',
  targetPlatform: 'android',
  targetVersion: '1.0.0'
})
const comparing = ref(false)
const compareResult = ref([])
const compareSourceCount = ref(0)
const compareTargetCount = ref(0)
const compareOnlyInSource = ref(0)
const compareOnlyInTarget = ref(0)
const compareDifferent = ref(0)
const activeCompareTab = ref('different')
const isFullscreen = ref(false)

// 数据同步相关
const syncDialogVisible = ref(false)
const syncFormRef = ref(null)
const syncForm = reactive({
  sourcePlatform: 'ios',
  sourceVersion: '1.0.0',
  targetPlatform: 'android',
  targetVersion: '1.0.0',
  options: ['add', 'update'] // 默认选项
})
const syncRules = {
  sourcePlatform: [
    { required: true, message: '请选择源平台', trigger: 'change' }
  ],
  sourceVersion: [
    { required: true, message: '请输入源版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ],
  targetPlatform: [
    { required: true, message: '请选择目标平台', trigger: 'change' }
  ],
  targetVersion: [
    { required: true, message: '请输入目标版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ]
}
const syncing = ref(false)

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    
    const params = {
      ...queryParams
    }
    
    // 将空字符串转为null，避免后端转换问题
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        params[key] = null
      }
    })
    
    const res = await getStyleCategoryList(params)
    if (res.code === 200 && res.data) {
      styleCategoryList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取样式分类关联列表成功:', styleCategoryList.value)
    } else {
      ElMessage.error(res.message || '获取样式分类关联列表失败')
    }
  } catch (error) {
    console.error('获取样式分类关联列表异常:', error)
    ElMessage.error('获取样式分类关联列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 刷新数据
const handleRefresh = () => {
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.styleId = ''
  queryParams.categoryId = ''
  queryParams.platform = ''
  queryParams.version = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getStyleCategoryDetail(row.id)
    if (res.code === 200 && res.data) {
      currentDetail.value = res.data
    } else {
      // 使用列表数据
      currentDetail.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取样式分类关联详情异常:', error)
    ElMessage.error('获取样式分类关联详情失败，请重试')
    // 使用列表数据作为备选
    currentDetail.value = { ...row }
    detailVisible.value = true
  }
}

// 添加样式分类关联
const handleAdd = () => {
  dialogTitle.value = '添加样式分类关联'
  dialogVisible.value = true
  resetForm()
}

// 编辑样式分类关联
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑样式分类关联'
    
    // 获取详细信息
    const res = await getStyleCategoryDetail(row.id)
    if (res.code === 200 && res.data) {
      const detail = res.data
      
      // 填充表单
      form.id = detail.id
      form.styleId = detail.styleId
      form.categoryId = detail.categoryId
      form.platform = detail.platform
      form.version = detail.version
      form.sortOrder = detail.sortOrder
      
      dialogVisible.value = true
    } else {
      ElMessage.error('获取样式分类关联详情失败')
    }
  } catch (error) {
    console.error('获取样式分类关联详情异常:', error)
    ElMessage.error('获取样式分类关联详情失败，请重试')
  }
}

// 删除样式分类关联
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除ID为"${row.id}"的样式分类关联吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteStyleCategory(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      
      // 重新加载数据
      getList()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除样式分类关联异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.styleId = null
  form.categoryId = null
  form.platform = 'ios'
  form.version = '1.0.0'
  form.sortOrder = 0
  
  // 重置表单校验结果
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const data = {
      styleId: form.styleId,
      categoryId: form.categoryId,
      platform: form.platform,
      version: form.version,
      sortOrder: form.sortOrder
    }
    
    let res
    if (form.id) {
      // 更新
      data.id = form.id
      res = await updateStyleCategory(form.id, data)
    } else {
      // 创建
      res = await createStyleCategory(data)
    }
    
    if (res.code === 200) {
      ElMessage.success(`${form.id ? '更新' : '添加'}成功`)
      dialogVisible.value = false
      
      // 重新加载数据
      getList()
    } else {
      ElMessage.error(res.message || `${form.id ? '更新' : '添加'}失败`)
    }
  } catch (error) {
    console.error(`${form.id ? '更新' : '添加'}样式分类关联异常:`, error)
    ElMessage.error(`${form.id ? '更新' : '添加'}失败，请检查表单内容`)
  } finally {
    submitting.value = false
  }
}

// 打开数据对比对话框
const handleCompare = () => {
  isFullscreen.value = true // 设置为全屏模式
  compareDialogVisible.value = true
  compareResult.value = []
}

// 执行数据对比
const handleCompareData = async () => {
  try {
    comparing.value = true
    compareResult.value = []
    
    // 获取源数据
    const sourceParams = {
      platform: compareForm.sourcePlatform,
      version: compareForm.sourceVersion,
      pageSize: 1000 // 大页面尺寸以获取更多数据
    }
    
    // 获取目标数据
    const targetParams = {
      platform: compareForm.targetPlatform,
      version: compareForm.targetVersion,
      pageSize: 1000
    }
    
    const [sourceRes, targetRes] = await Promise.all([
      getStyleCategoryByPlatformVersion(sourceParams),
      getStyleCategoryByPlatformVersion(targetParams)
    ])
    
    if (sourceRes.code !== 200 || targetRes.code !== 200) {
      ElMessage.error('获取数据失败')
      return
    }
    
    const sourceData = sourceRes.data || []
    const targetData = targetRes.data || []
    
    compareSourceCount.value = sourceData.length
    compareTargetCount.value = targetData.length
    
    // 构建索引
    const targetMap = {}
    targetData.forEach(item => {
      const key = `${item.styleId}-${item.categoryId}`
      targetMap[key] = item
    })
    
    const sourceMap = {}
    const result = []
    
    // 分析源数据
    sourceData.forEach(source => {
      const key = `${source.styleId}-${source.categoryId}`
      sourceMap[key] = source
      
      if (targetMap[key]) {
        // 存在于两边
        const target = targetMap[key]
        
        // 检查是否有差异
        let isDifferent = false
        
        if (source.sortOrder !== target.sortOrder) {
          isDifferent = true
        }
        
        result.push({
          styleTitle: source.styleTitle,
          categoryName: source.categoryName,
          diffType: isDifferent ? 'different' : 'same',
          source,
          target
        })
      } else {
        // 只在源中存在
        result.push({
          styleTitle: source.styleTitle,
          categoryName: source.categoryName,
          diffType: 'source_only',
          source,
          target: null
        })
      }
    })
    
    // 找出只在目标中存在的数据
    targetData.forEach(target => {
      const key = `${target.styleId}-${target.categoryId}`
      if (!sourceMap[key]) {
        result.push({
          styleTitle: target.styleTitle,
          categoryName: target.categoryName,
          diffType: 'target_only',
          source: null,
          target
        })
      }
    })
    
    // 计算统计数据
    compareOnlyInSource.value = result.filter(item => item.diffType === 'source_only').length
    compareOnlyInTarget.value = result.filter(item => item.diffType === 'target_only').length
    compareDifferent.value = result.filter(item => item.diffType === 'different').length
    
    // 设置结果
    compareResult.value = result
  } catch (error) {
    console.error('数据对比异常:', error)
    ElMessage.error('数据对比失败，请重试')
  } finally {
    comparing.value = false
  }
}

// 处理单项同步
const handleSyncItem = async (sourceData, operation, targetData = null) => {
  try {
    // 验证数据有效性
    if ((operation === 'add' || operation === 'update') && !sourceData) {
      ElMessage.error('源数据不存在，无法执行同步操作');
      return;
    }
    
    if (operation === 'delete' && !sourceData) {
      ElMessage.error('目标数据不存在，无法执行删除操作');
      return;
    }
    
    if (operation === 'update' && !targetData) {
      ElMessage.error('目标数据不存在，无法执行更新操作');
      return;
    }
    
    // 确认同步操作
    try {
      let confirmMessage = '';
      if (operation === 'add') {
        confirmMessage = '确定要将此项同步到目标平台/版本吗？';
      } else if (operation === 'update') {
        confirmMessage = '确定要用源数据更新目标数据吗？';
      } else if (operation === 'delete') {
        confirmMessage = '确定要从目标平台/版本删除此项吗？';
      }
      
      await ElMessageBox.confirm(
        confirmMessage,
        '确认同步',
        { type: 'warning' }
      );
    } catch (e) {
      return; // 用户取消
    }
    
    syncing.value = true;
    
    // 构造符合后端 StyleCategorySyncDTO 的数据结构
    const syncData = {
      sourcePlatform: compareForm.sourcePlatform,
      sourceVersion: compareForm.sourceVersion,
      targetPlatform: compareForm.targetPlatform,
      targetVersion: compareForm.targetVersion,
      options: {
        addMissing: operation === 'add',
        updateDifferent: operation === 'update',
        deleteExtra: operation === 'delete'
      }
    };
    
    // 添加要同步的数据ID
    if (operation === 'add' || operation === 'update') {
      syncData.dataIds = [sourceData.id];
    } else if (operation === 'delete') {
      syncData.dataIds = [targetData ? targetData.id : sourceData.id];
    }
    
    const res = await syncStyleCategoryData(syncData);
    
    if (res.code === 200) {
      // 使用返回的StyleCategorySyncResultVO展示更详细的同步结果
      const result = res.data;
      ElMessage.success(`同步成功，共处理${result.totalCount}条数据`);
      // 重新对比数据
      handleCompareData();
    } else {
      ElMessage.error(res.message || '同步失败');
    }
  } catch (error) {
    console.error('同步数据异常:', error);
    ElMessage.error('同步失败，请重试');
  } finally {
    syncing.value = false;
  }
};

// 打开批量同步对话框
const handleSync = () => {
  syncDialogVisible.value = true
}

// 提交同步表单
const submitSyncForm = async () => {
  if (!syncFormRef.value) return
  
  try {
    await syncFormRef.value.validate()
    
    // 检查是否选择了同步选项
    if (syncForm.options.length === 0) {
      ElMessage.warning('请至少选择一个同步选项')
      return
    }
    
    // 确认是否继续
    try {
      await ElMessageBox.confirm(
        '同步可能会导致目标数据的变更，确定要继续吗?',
        '确认同步',
        { type: 'warning' }
      )
    } catch (e) {
      return // 用户取消
    }
    
    syncing.value = true
    
    // 转换选项格式以匹配后端的 StyleCategorySyncDTO
    const data = {
      sourcePlatform: syncForm.sourcePlatform,
      sourceVersion: syncForm.sourceVersion,
      targetPlatform: syncForm.targetPlatform,
      targetVersion: syncForm.targetVersion,
      options: {
        addMissing: syncForm.options.includes('add'),
        updateDifferent: syncForm.options.includes('update'),
        deleteExtra: syncForm.options.includes('delete')
      }
    }
    
    const res = await syncStyleCategoryData(data)
    
    if (res.code === 200) {
      // 使用返回的StyleCategorySyncResultVO展示更详细的同步结果
      const result = res.data;
      ElMessage.success(`同步成功，共同步${result.totalCount}条数据（新增${result.addCount}条，更新${result.updateCount}条）`);
      syncDialogVisible.value = false
      
      // 刷新列表
      getList()
    } else {
      ElMessage.error(res.message || '同步失败')
    }
  } catch (error) {
    console.error('同步数据异常:', error)
    ElMessage.error('同步失败，请重试')
  } finally {
    syncing.value = false
  }
}

// 同步全部数据
const handleSyncAll = async () => {
  try {
    syncing.value = true
    
    const data = {
      sourcePlatform: compareForm.sourcePlatform,
      sourceVersion: compareForm.sourceVersion,
      targetPlatform: compareForm.targetPlatform,
      targetVersion: compareForm.targetVersion,
      options: {
        addMissing: true,
        updateDifferent: true,
        deleteExtra: false
      }
    }
    
    const res = await syncStyleCategoryData(data)
    
    if (res.code === 200) {
      // 使用返回的StyleCategorySyncResultVO展示更详细的同步结果
      const result = res.data;
      ElMessage.success(`同步成功，共同步${result.totalCount}条数据（新增${result.addCount}条，更新${result.updateCount}条）`);
      compareDialogVisible.value = false
      
      // 刷新列表
      getList()
    } else {
      ElMessage.error(res.message || '同步失败')
    }
  } catch (error) {
    console.error('同步全部数据异常:', error)
    ElMessage.error('同步全部数据失败，请重试')
  } finally {
    syncing.value = false
  }
}

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.style-category-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

// 数据对比相关样式
.compare-form {
  margin-bottom: 20px;
}

.compare-empty {
  padding: 30px 0;
}

.compare-result {
  h4 {
    margin-top: 0;
    margin-bottom: 15px;
  }
  
  .summary {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
    }
    
    .summary-actions {
      margin-top: 10px;
      text-align: right;
    }
  }
}

// 差异对比相关样式
.diff-item {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  
  .diff-title {
    display: flex;
    align-items: center;
    
    .item-title {
      font-weight: bold;
      margin-right: 10px;
    }
  }
}

.diff-content {
  display: flex;
  min-height: 200px;
  
  .diff-source,
  .diff-target {
    flex: 1;
    padding: 0;
    
    &:first-child {
      border-right: 1px dashed #dcdfe6;
    }
  }
  
  .diff-panel-header {
    padding: 8px 15px;
    font-weight: bold;
    background-color: #f8f8f8;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    
    &::before {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    
    .diff-source &::before {
      background-color: #409eff;
    }
    
    .diff-target &::before {
      background-color: #67c23a;
    }
  }
  
  .diff-panel-content {
    padding: 15px;
    
    &.diff-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 200px;
    }
  }
}

// 高亮差异项
.highlight-diff {
  background-color: #ffeaea !important;
  color: #f56c6c;
  font-weight: bold;
  position: relative;
  
  &::after {
    content: "!";
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #f56c6c;
    color: #fff;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }
}

.diff-empty-state {
  padding: 30px 0;
}

// 调整对话框最大高度
:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

// 自定义对话框标题栏
.dialog-custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

.fullscreen-btn {
  margin-right: -8px;
  
  &:hover {
    color: #409eff;
  }
}

// 全屏模式下的样式调整
:deep(.el-dialog--fullscreen) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  
  .el-dialog__header {
    padding: 15px 20px;
  }
  
  .el-dialog__body {
    flex: 1;
    height: calc(100vh - 110px);
    max-height: none;
    margin: 0;
    padding-bottom: 20px;
    overflow: auto;
  }
}
</style> 