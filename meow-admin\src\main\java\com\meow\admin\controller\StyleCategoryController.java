package com.meow.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.StyleCategoryDTO;
import com.meow.admin.model.dto.StyleCategorySortExportDTO;
import com.meow.admin.model.dto.StyleCategorySortImportDTO;
import com.meow.admin.model.dto.StyleCategorySyncDTO;
import com.meow.admin.model.entity.StyleCategory.PlatformType;
import com.meow.admin.model.param.StyleCategoryQueryParam;
import com.meow.admin.model.vo.StyleCategoryComparisonResultVO;
import com.meow.admin.model.vo.StyleCategorySyncResultVO;
import com.meow.admin.model.vo.StyleCategoryVO;
import com.meow.admin.service.StyleCategoryService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 样式分类关联控制器
 */
@Tag(name = "样式分类关联接口")
@RestController
@RequestMapping("/api/style-category")
@RequiredArgsConstructor
@Slf4j
public class StyleCategoryController {
    
    private final StyleCategoryService styleCategoryService;
    
    /**
     * 分页查询样式分类关联列表
     */
    @Operation(summary = "分页查询样式分类关联列表")
    @GetMapping("/list")
    public Result<IPage<StyleCategoryVO>> list(StyleCategoryQueryParam param) {
        IPage<StyleCategoryVO> page = styleCategoryService.getStyleCategoryList(param);
        return Result.success(page);
    }
    
    /**
     * 根据ID获取样式分类关联详情
     */
    @Operation(summary = "获取样式分类关联详情")
    @GetMapping("/{id}")
    public Result<StyleCategoryVO> getById(@PathVariable("id") Long id) {
        StyleCategoryVO styleCategory = styleCategoryService.getStyleCategoryById(id);
        return Result.success(styleCategory);
    }
    
    /**
     * 根据样式ID获取关联分类列表
     */
    @Operation(summary = "获取样式关联的分类列表")
    @GetMapping("/style/{styleId}")
    public Result<List<StyleCategoryVO>> getByStyleId(
            @PathVariable("styleId") Long styleId,
            @RequestParam(value = "platform", required = false)
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam(value = "version", required = false) String version) {
        List<StyleCategoryVO> styleCategories = styleCategoryService.getStyleCategoriesByStyleId(styleId, platform, version);
        return Result.success(styleCategories);
    }
    
    /**
     * 根据分类ID获取关联样式列表
     */
    @Operation(summary = "获取分类关联的样式列表")
    @GetMapping("/category/{categoryId}")
    public Result<List<StyleCategoryVO>> getByCategoryId(
            @PathVariable("categoryId") Long categoryId,
            @RequestParam(value = "platform", required = false)
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam(value = "version", required = false) String version) {
        List<StyleCategoryVO> styleCategories = styleCategoryService.getStyleCategoriesByCategoryId(categoryId, platform, version);
        return Result.success(styleCategories);
    }
    
    /**
     * 创建样式分类关联
     */
    @Operation(summary = "创建样式分类关联")
    @PostMapping
    public Result<StyleCategoryVO> create(@Valid @RequestBody StyleCategoryDTO dto) {
        StyleCategoryVO styleCategory = styleCategoryService.createStyleCategory(dto);
        return Result.success(styleCategory);
    }
    
    /**
     * 更新样式分类关联
     */
    @Operation(summary = "更新样式分类关联")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody StyleCategoryDTO dto) {
        boolean result = styleCategoryService.updateStyleCategory(id, dto);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 删除样式分类关联
     */
    @Operation(summary = "删除样式分类关联")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = styleCategoryService.deleteStyleCategory(id);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 根据平台和版本获取样式分类关联列表
     */
    @Operation(summary = "根据平台和版本获取样式分类关联列表")
    @GetMapping("/platform-version")
    public Result<List<StyleCategoryVO>> getByPlatformVersion(
            @RequestParam("platform") 
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam("version") String version) {
        List<StyleCategoryVO> styleCategories = styleCategoryService.getStyleCategoryByPlatformVersion(platform, version);
        return Result.success(styleCategories);
    }
    
    /**
     * 比较两个平台版本之间的样式分类关联数据差异
     */
    @Operation(summary = "比较两个平台版本之间的样式分类关联数据差异")
    @GetMapping("/compare")
    public Result<StyleCategoryComparisonResultVO> compareData(
            @RequestParam("sourcePlatform") 
            @Parameter(description = "源平台类型，可选值：ios, android") PlatformType sourcePlatform,
            @RequestParam("sourceVersion") String sourceVersion,
            @RequestParam("targetPlatform") 
            @Parameter(description = "目标平台类型，可选值：ios, android") PlatformType targetPlatform,
            @RequestParam("targetVersion") String targetVersion) {
        StyleCategoryComparisonResultVO result = styleCategoryService.compareStyleCategoryData(
                sourcePlatform, sourceVersion, targetPlatform, targetVersion);
        return Result.success(result);
    }
    
    /**
     * 同步样式分类关联数据
     */
    @Operation(summary = "同步样式分类关联数据")
    @PostMapping("/sync")
    public Result<StyleCategorySyncResultVO> syncData(@Valid @RequestBody StyleCategorySyncDTO syncDTO) {
        StyleCategorySyncResultVO result = styleCategoryService.syncStyleCategoryData(syncDTO);
        return Result.success(result);
    }
    
    /**
     * 根据平台和版本批量删除样式分类关联
     */
    @Operation(summary = "根据平台和版本批量删除样式分类关联")
    @DeleteMapping("/batch")
    public Result<Integer> deleteBatchByPlatformVersion(
            @RequestParam String platform,
            @RequestParam String version) {
        log.info("批量删除样式分类关联 | platform={}, version={}", platform, version);
        
        int count = styleCategoryService.deleteBatchByPlatformVersion(platform, version);
        
        return Result.success(count);
    }

    /**
     * 导入排序Excel文件
     */
    @Operation(summary = "导入排序Excel文件")
    @PostMapping("/import-sort")
    public Result<String> importSortOrder(@RequestParam("file") MultipartFile file) {
        log.info("开始导入排序Excel文件: {}", file.getOriginalFilename());

        try {
            // 验证文件
            if (file.isEmpty()) {
                return Result.failed("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return Result.failed("请上传Excel文件(.xlsx或.xls格式)");
            }

            // 读取Excel数据
            List<StyleCategorySortImportDTO> importList = EasyExcel.read(file.getInputStream())
                    .head(StyleCategorySortImportDTO.class)
                    .sheet()
                    .doReadSync();

            if (importList.isEmpty()) {
                return Result.failed("Excel文件中没有数据");
            }

            log.info("读取到{}条数据", importList.size());

            // 调用服务层处理导入数据
            String result = styleCategoryService.importSortOrder(importList);

            log.info("导入排序完成: {}", result);
            return Result.success(result);

        } catch (Exception e) {
            log.error("导入排序失败", e);
            return Result.failed("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出分类排序Excel文件
     */
    @Operation(summary = "导出分类排序Excel文件")
    @GetMapping("/export-sort/{categoryId}")
    public ResponseEntity<byte[]> exportCategorySortOrder(@PathVariable("categoryId") Long categoryId) {
        log.info("开始导出分类排序Excel文件: categoryId={}", categoryId);

        try {
            // 获取分类排序数据
            List<StyleCategorySortExportDTO> exportData = styleCategoryService.getCategorySortOrderForExport(categoryId);

            if (exportData.isEmpty()) {
                log.warn("分类ID {}没有找到排序数据", categoryId);
                return ResponseEntity.notFound().build();
            }

            // 创建Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, StyleCategorySortExportDTO.class)
                    .sheet("分类排序")
                    .doWrite(exportData);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("分类排序_%d_%s.xlsx", categoryId, timestamp);
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);

            log.info("导出分类排序完成: categoryId={}, 数据量={}", categoryId, exportData.size());

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());

        } catch (Exception e) {
            log.error("导出分类排序异常: categoryId={}", categoryId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}